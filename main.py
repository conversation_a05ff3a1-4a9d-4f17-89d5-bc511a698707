from fastapi import FastAP<PERSON>, UploadFile, HTTPException
from google import genai
from google.genai import types
import os
from typing import List
import json


# Create FastAPI instance
app = FastAPI(
    title="Whatsapp API builder",
    description="A Whatsapp API builder to answer customer enqiries",
    version="1.0.0"
)

os.environ["GEMINI_API_KEY"] = "AIzaSyDnE-AWNMd7BcTEWPExq7MO0JvMT8lVLos"

client = genai.Client(api_key=os.environ["GEMINI_API_KEY"])


@app.post("/generate-faq-questions")
async def generate_faq_questions(images: List[UploadFile]):
    try:
        request = "Your role is a bot builder Use the following instructions: " \
            "1- Use only the same images language in the response output" \
            "2- Generate FAQ questions with answers from the list of images provided." \
            "3- The questions should be short and to the point." \
            "4- The questions should answer all customer ordering these products enqiries."\
            "5- Don't make redundaunt questions." \
            "6- The questions should be in the same language as the images."
        print("request:" + request)

        # Process uploaded images
        image_parts = []
        for image in images:
            # Read the image content
            image_content = await image.read()

            # Create a proper image part for Gemini API
            image_part = types.Part.from_bytes(
                data=image_content,
                mime_type=image.content_type or "image/jpeg"
            )
            image_parts.append(image_part)

        # Create the content list with text and images

        response = client.models.generate_content(
            model="gemini-2.0-flash-exp",
            config=types.GenerateContentConfig(
                response_mime_type='application/json',
                response_schema=list[str],
            ),
            contents=[request, image_parts]
        )

        return {"faq_questions": json.dumps(response.text, ensure_ascii=False, indent=2)}

    except Exception as e:
        print(f"Error: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Error generating FAQ questions: {str(e)}")
