from fastapi import <PERSON><PERSON><PERSON>
from fastapi import UploadFile
from google import genai
from google.genai.types import GenerateContentConfig
from google.genai import types
import os


# Create FastAPI instance
app = FastAPI(
    title="Simple FastAPI App",
    description="A FastAPI application with a single endpoint",
    version="1.0.0"
)

os.environ["GEMINI_API_KEY"] = "AIzaSyDnE-AWNMd7BcTEWPExq7MO0JvMT8lVLos"

client = genai.Client(api_key=os.environ["GEMINI_API_KEY"])
if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)


@app.post("/generate-faq-questions")
def generate_faq_questions(images: list[UploadFile]):
    request = "Your role is a bot builder Generate 5 FAQ questions from the list of images "
    response = client.models.generate_content(contents=[request, images],
                                              config=types.GenerateContentConfig(response_schema=list[str], response_mime_type='application/json'
                                                                                 ))
    return response
